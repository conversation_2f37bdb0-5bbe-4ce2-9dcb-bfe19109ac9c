import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';
import '../../routes/app_routes.dart';
import '../../utils/responsive_layout.dart';
import 'app_logo.dart';

class ModernDrawer extends StatelessWidget {
  final bool isAdmin;

  const ModernDrawer({super.key, this.isAdmin = false});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: AppColors.surfaceWhite,
      child: Column(
        children: [
          _buildDrawerHeader(context),
          Expanded(child: _buildDrawerItems(context)),
          _buildDrawerFooter(context),
        ],
      ),
    );
  }

  Widget _buildDrawerHeader(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return Container(
          height: 200,
          decoration: const BoxDecoration(gradient: AppColors.primaryGradient),
          child: <PERSON><PERSON><PERSON>(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const AppLogo(
                    type: LogoType.compact,
                    width: 120,
                    height: 40,
                    color: AppColors.textWhite,
                  ),
                  const Spacer(),
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 25,
                        backgroundColor: AppColors.textWhite.withValues(
                          alpha: 0.2,
                        ),
                        child: Text(
                          authProvider.userInitials,
                          style: const TextStyle(
                            color: AppColors.textWhite,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              authProvider.displayName,
                              style: const TextStyle(
                                color: AppColors.textWhite,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            Text(
                              authProvider.roleDisplayText,
                              style: TextStyle(
                                color: AppColors.textWhite.withValues(
                                  alpha: 0.8,
                                ),
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDrawerItems(BuildContext context) {
    return ListView(
      padding: const EdgeInsets.symmetric(vertical: 8),
      children: [
        if (isAdmin)
          ..._buildAdminItems(context)
        else
          ..._buildUserItems(context),
        _buildSectionDivider('الإعدادات والمساعدة'),
        _buildDrawerItem(
          context,
          icon: Icons.settings,
          title: 'الإعدادات',
          onTap: () {
            Navigator.pop(context);
            Navigator.pushNamed(context, AppRoutes.settingsScreen);
          },
        ),
        _buildDrawerItem(
          context,
          icon: Icons.help_outline,
          title: 'المساعدة',
          onTap: () {
            Navigator.pop(context);
            Navigator.pushNamed(context, AppRoutes.about);
          },
        ),
        _buildDrawerItem(
          context,
          icon: Icons.info_outline,
          title: 'حول التطبيق',
          onTap: () {
            Navigator.pop(context);
            Navigator.pushNamed(context, AppRoutes.about);
          },
        ),
      ],
    );
  }

  Widget _buildSectionDivider(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: AppColors.textSecondary,
          letterSpacing: 0.5,
        ),
      ),
    );
  }

  List<Widget> _buildAdminItems(BuildContext context) {
    return [
      // Main Dashboard
      _buildDrawerItem(
        context,
        icon: Icons.dashboard_outlined,
        title: 'لوحة التحكم',
        onTap: () {
          Navigator.pop(context);
          Navigator.pushReplacementNamed(context, AppRoutes.adminDashboard);
        },
        isSelected:
            ModalRoute.of(context)?.settings.name == AppRoutes.adminDashboard,
      ),

      // Management Section
      _buildSectionDivider('الإدارة'),
      _buildDrawerItem(
        context,
        icon: Icons.people_outline,
        title: 'إدارة الموظفين',
        onTap: () {
          Navigator.pop(context);
          Navigator.pushNamed(context, AppRoutes.employeesManagement);
        },
        isSelected:
            ModalRoute.of(context)?.settings.name ==
            AppRoutes.employeesManagement,
      ),
      _buildDrawerItem(
        context,
        icon: Icons.person_add_outlined,
        title: 'إضافة موظف',
        onTap: () {
          Navigator.pop(context);
          Navigator.pushNamed(context, AppRoutes.addEmployee);
        },
        isSubItem: true,
      ),
      _buildDrawerItem(
        context,
        icon: Icons.location_on_outlined,
        title: 'إدارة المواقع',
        onTap: () {
          Navigator.pop(context);
          Navigator.pushNamed(context, AppRoutes.sitesManagement);
        },
        isSelected:
            ModalRoute.of(context)?.settings.name == AppRoutes.sitesManagement,
      ),
      _buildDrawerItem(
        context,
        icon: Icons.add_location_outlined,
        title: 'إضافة موقع',
        onTap: () {
          Navigator.pop(context);
          Navigator.pushNamed(context, AppRoutes.addSite);
        },
        isSubItem: true,
      ),

      // Attendance Section
      _buildSectionDivider('الحضور والمراقبة'),
      _buildDrawerItem(
        context,
        icon: Icons.access_time_outlined,
        title: 'إدارة الحضور',
        onTap: () {
          Navigator.pop(context);
          Navigator.pushNamed(context, AppRoutes.pointagesManagement);
        },
        isSelected:
            ModalRoute.of(context)?.settings.name ==
            AppRoutes.pointagesManagement,
      ),
      _buildDrawerItem(
        context,
        icon: Icons.monitor_outlined,
        title: 'المراقبة المباشرة',
        onTap: () {
          Navigator.pop(context);
          Navigator.pushNamed(context, AppRoutes.monitoringScreen);
        },
        isSelected:
            ModalRoute.of(context)?.settings.name == AppRoutes.monitoringScreen,
      ),

      // Reports Section
      _buildSectionDivider('التقارير والتحليلات'),
      _buildDrawerItem(
        context,
        icon: Icons.assessment_outlined,
        title: 'التقارير',
        onTap: () {
          Navigator.pop(context);
          Navigator.pushNamed(context, AppRoutes.reportsScreen);
        },
        isSelected:
            ModalRoute.of(context)?.settings.name == AppRoutes.reportsScreen,
      ),
    ];
  }

  List<Widget> _buildUserItems(BuildContext context) {
    return [
      // Main Dashboard
      _buildDrawerItem(
        context,
        icon: Icons.dashboard_outlined,
        title: 'لوحة التحكم',
        onTap: () {
          Navigator.pop(context);
          Navigator.pushReplacementNamed(context, AppRoutes.userDashboard);
        },
        isSelected:
            ModalRoute.of(context)?.settings.name == AppRoutes.userDashboard,
      ),

      // Attendance Section
      _buildSectionDivider('الحضور'),
      _buildDrawerItem(
        context,
        icon: Icons.access_time_outlined,
        title: 'تسجيل الحضور',
        onTap: () {
          Navigator.pop(context);
          Navigator.pushNamed(context, AppRoutes.attendanceScreen);
        },
        isSelected:
            ModalRoute.of(context)?.settings.name == AppRoutes.attendanceScreen,
      ),
      _buildDrawerItem(
        context,
        icon: Icons.monitor_outlined,
        title: 'مراقبة الحضور',
        onTap: () {
          Navigator.pop(context);
          Navigator.pushNamed(context, AppRoutes.monitoringScreen);
        },
        isSelected:
            ModalRoute.of(context)?.settings.name == AppRoutes.monitoringScreen,
      ),

      // Profile Section
      _buildSectionDivider('الملف الشخصي'),
      _buildDrawerItem(
        context,
        icon: Icons.person_outlined,
        title: 'الملف الشخصي',
        onTap: () {
          Navigator.pop(context);
          Navigator.pushNamed(context, AppRoutes.profileScreen);
        },
        isSelected:
            ModalRoute.of(context)?.settings.name == AppRoutes.profileScreen,
      ),
      _buildDrawerItem(
        context,
        icon: Icons.lock_outlined,
        title: 'تغيير كلمة المرور',
        onTap: () {
          Navigator.pop(context);
          Navigator.pushNamed(context, AppRoutes.changePassword);
        },
        isSubItem: true,
      ),
    ];
  }

  Widget _buildDrawerItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isSelected = false,
    bool isSubItem = false,
  }) {
    final isCurrentlySelected = isSelected;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: isSubItem ? 24 : 8, vertical: 2),
      child: ListTile(
        leading: Icon(
          icon,
          color: isCurrentlySelected
              ? AppColors.primaryBlue
              : isSubItem
              ? AppColors.textSecondary
              : AppColors.textPrimary,
          size: isSubItem ? 20 : 24,
        ),
        title: Text(
          title,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            fontWeight: isCurrentlySelected ? FontWeight.w600 : FontWeight.w500,
            color: isCurrentlySelected
                ? AppColors.primaryBlue
                : isSubItem
                ? AppColors.textSecondary
                : AppColors.textPrimary,
            fontSize: isSubItem ? 14 : 16,
          ),
        ),
        onTap: onTap,
        selected: isCurrentlySelected,
        selectedTileColor: AppColors.primaryBlue.withValues(alpha: 0.1),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        contentPadding: EdgeInsets.symmetric(
          horizontal: isSubItem ? 12 : 16,
          vertical: isSubItem ? 2 : 4,
        ),
        visualDensity: isSubItem
            ? VisualDensity.compact
            : VisualDensity.standard,
      ),
    );
  }

  Widget _buildDrawerFooter(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: AppColors.textHint.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.logout, color: AppColors.error),
            title: const Text(
              'تسجيل الخروج',
              style: TextStyle(
                color: AppColors.error,
                fontWeight: FontWeight.w600,
              ),
            ),
            onTap: () => _showLogoutDialog(context),
            contentPadding: const EdgeInsets.symmetric(horizontal: 4),
          ),
          const SizedBox(height: 8),
          Text(
            'ClockIn v1.0.0',
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: AppColors.textHint),
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context); // Close drawer
              context.read<AuthProvider>().logout();
              Navigator.pushReplacementNamed(context, AppRoutes.login);
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }
}
