import 'package:flutter/material.dart';
import 'package:data_table_2/data_table_2.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../utils/responsive_layout.dart';

/// Enhanced responsive data table for admin screens
class ResponsiveDataTable<T> extends StatelessWidget {
  final List<ResponsiveDataColumn> columns;
  final List<T> data;
  final Widget Function(T item, int index) rowBuilder;
  final String? emptyMessage;
  final Widget? emptyIcon;
  final VoidCallback? onRefresh;
  final bool isLoading;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? minWidth;
  final bool showBorder;
  final bool showHeader;

  const ResponsiveDataTable({
    super.key,
    required this.columns,
    required this.data,
    required this.rowBuilder,
    this.emptyMessage,
    this.emptyIcon,
    this.onRefresh,
    this.isLoading = false,
    this.padding,
    this.margin,
    this.minWidth,
    this.showBorder = true,
    this.showHeader = true,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, deviceType) {
        final tableConfig = ResponsiveLayout.getTableConfig(context);
        
        if (data.isEmpty && !isLoading) {
          return _buildEmptyState(context);
        }

        return Container(
          margin: margin ?? ResponsiveLayout.getResponsivePadding(context),
          decoration: showBorder ? BoxDecoration(
            color: AppColors.surfaceWhite,
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            border: Border.all(
              color: AppColors.cardBorder.withValues(alpha: 0.2),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.cardShadow.withValues(alpha: 0.08),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ) : null,
          child: _buildTable(context, deviceType, tableConfig),
        );
      },
    );
  }

  Widget _buildTable(BuildContext context, DeviceType deviceType, TableConfig config) {
    if (deviceType == DeviceType.mobile) {
      return _buildMobileList(context);
    }

    return SingleChildScrollView(
      scrollDirection: config.horizontalScrollEnabled ? Axis.horizontal : Axis.vertical,
      child: ConstrainedBox(
        constraints: BoxConstraints(
          minWidth: minWidth ?? AppConstants.minTableWidth,
        ),
        child: DataTable2(
          columnSpacing: config.columnSpacing,
          horizontalMargin: config.horizontalMargin,
          minWidth: minWidth ?? AppConstants.minTableWidth,
          headingRowHeight: AppConstants.tableHeaderHeight,
          dataRowHeight: AppConstants.tableRowHeight,
          headingRowColor: WidgetStateProperty.all(
            AppColors.surfaceGrey.withValues(alpha: 0.5),
          ),
          columns: _buildDataColumns(context, config),
          rows: _buildDataRows(context),
          showCheckboxColumn: false,
          border: showBorder ? TableBorder.all(
            color: AppColors.borderLight.withValues(alpha: 0.3),
            width: 1,
          ) : null,
        ),
      ),
    );
  }

  Widget _buildMobileList(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: padding ?? const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: data.length,
      itemBuilder: (context, index) {
        return Card(
          margin: const EdgeInsets.only(bottom: AppConstants.smallPadding),
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: rowBuilder(data[index], index),
          ),
        );
      },
    );
  }

  List<DataColumn2> _buildDataColumns(BuildContext context, TableConfig config) {
    return columns.where((col) => config.showAllColumns || col.showOnMobile).map((col) {
      return DataColumn2(
        label: Text(
          col.label,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: ResponsiveLayout.getResponsiveFontSize(context, 14),
            color: AppColors.textPrimary,
          ),
        ),
        size: col.size,
        fixedWidth: col.fixedWidth,
        numeric: col.numeric,
        tooltip: col.tooltip,
      );
    }).toList();
  }

  List<DataRow> _buildDataRows(BuildContext context) {
    return data.asMap().entries.map((entry) {
      final index = entry.key;
      final item = entry.value;
      
      return DataRow(
        color: WidgetStateProperty.resolveWith((states) {
          if (index.isEven) {
            return AppColors.surfaceGrey.withValues(alpha: 0.3);
          }
          return null;
        }),
        cells: [
          DataCell(rowBuilder(item, index)),
        ],
      );
    }).toList();
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      padding: ResponsiveLayout.getResponsivePadding(context),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            emptyIcon ?? Icon(
              Icons.inbox_outlined,
              size: ResponsiveLayout.getResponsiveSpacing(context, 80),
              color: AppColors.textHint,
            ),
            SizedBox(height: ResponsiveLayout.getResponsiveSpacing(context, 16)),
            Text(
              emptyMessage ?? 'لا توجد بيانات للعرض',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.textSecondary,
                fontSize: ResponsiveLayout.getResponsiveFontSize(context, 16),
              ),
              textAlign: TextAlign.center,
            ),
            if (onRefresh != null) ...[
              SizedBox(height: ResponsiveLayout.getResponsiveSpacing(context, 16)),
              ElevatedButton.icon(
                onPressed: onRefresh,
                icon: const Icon(Icons.refresh),
                label: const Text('إعادة المحاولة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryBlue,
                  foregroundColor: AppColors.textWhite,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Responsive data column configuration
class ResponsiveDataColumn {
  final String label;
  final ColumnSize size;
  final double? fixedWidth;
  final bool numeric;
  final String? tooltip;
  final bool showOnMobile;

  const ResponsiveDataColumn({
    required this.label,
    this.size = ColumnSize.M,
    this.fixedWidth,
    this.numeric = false,
    this.tooltip,
    this.showOnMobile = true,
  });
}

/// Enhanced search and filter bar
class ResponsiveSearchBar extends StatelessWidget {
  final TextEditingController searchController;
  final String searchHint;
  final List<FilterOption>? filterOptions;
  final String? selectedFilter;
  final ValueChanged<String>? onSearchChanged;
  final ValueChanged<String?>? onFilterChanged;
  final VoidCallback? onRefresh;
  final Widget? trailing;
  final bool isLoading;

  const ResponsiveSearchBar({
    super.key,
    required this.searchController,
    this.searchHint = 'البحث...',
    this.filterOptions,
    this.selectedFilter,
    this.onSearchChanged,
    this.onFilterChanged,
    this.onRefresh,
    this.trailing,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, deviceType) {
        return Container(
          padding: ResponsiveLayout.getResponsivePadding(context),
          decoration: BoxDecoration(
            color: AppColors.surfaceWhite,
            boxShadow: [
              BoxShadow(
                color: AppColors.cardShadow.withValues(alpha: 0.1),
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              if (deviceType == DeviceType.mobile)
                _buildMobileLayout(context)
              else
                _buildDesktopLayout(context),
              if (trailing != null) ...[
                SizedBox(height: ResponsiveLayout.getResponsiveSpacing(context, 12)),
                trailing!,
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    return Column(
      children: [
        _buildSearchField(context),
        if (filterOptions != null) ...[
          SizedBox(height: ResponsiveLayout.getResponsiveSpacing(context, 12)),
          Row(
            children: [
              Expanded(child: _buildFilterDropdown(context)),
              if (onRefresh != null) ...[
                SizedBox(width: ResponsiveLayout.getResponsiveSpacing(context, 8)),
                _buildRefreshButton(context),
              ],
            ],
          ),
        ] else if (onRefresh != null) ...[
          SizedBox(height: ResponsiveLayout.getResponsiveSpacing(context, 12)),
          Align(
            alignment: Alignment.centerRight,
            child: _buildRefreshButton(context),
          ),
        ],
      ],
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: _buildSearchField(context),
        ),
        if (filterOptions != null) ...[
          SizedBox(width: ResponsiveLayout.getResponsiveSpacing(context, 16)),
          Expanded(child: _buildFilterDropdown(context)),
        ],
        if (onRefresh != null) ...[
          SizedBox(width: ResponsiveLayout.getResponsiveSpacing(context, 8)),
          _buildRefreshButton(context),
        ],
      ],
    );
  }

  Widget _buildSearchField(BuildContext context) {
    return TextField(
      controller: searchController,
      decoration: InputDecoration(
        hintText: searchHint,
        prefixIcon: const Icon(Icons.search),
        border: const OutlineInputBorder(),
        contentPadding: EdgeInsets.symmetric(
          horizontal: ResponsiveLayout.getResponsiveSpacing(context, 16),
          vertical: ResponsiveLayout.getResponsiveSpacing(context, 12),
        ),
      ),
      onChanged: onSearchChanged,
    );
  }

  Widget _buildFilterDropdown(BuildContext context) {
    if (filterOptions == null) return const SizedBox.shrink();

    return DropdownButtonFormField<String>(
      value: selectedFilter,
      decoration: const InputDecoration(
        labelText: 'تصفية',
        border: OutlineInputBorder(),
      ),
      items: filterOptions!.map((option) {
        return DropdownMenuItem(
          value: option.value,
          child: Text(option.label),
        );
      }).toList(),
      onChanged: onFilterChanged,
    );
  }

  Widget _buildRefreshButton(BuildContext context) {
    return IconButton(
      onPressed: isLoading ? null : onRefresh,
      icon: isLoading
          ? SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryBlue),
              ),
            )
          : const Icon(Icons.refresh),
      tooltip: 'تحديث',
    );
  }
}

/// Filter option for dropdown
class FilterOption {
  final String value;
  final String label;

  const FilterOption({
    required this.value,
    required this.label,
  });
}
