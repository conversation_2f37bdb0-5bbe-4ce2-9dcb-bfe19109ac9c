import 'package:flutter/material.dart';
import '../constants/app_constants.dart';

/// Responsive layout utility class for adaptive UI design
class ResponsiveLayout {
  /// Get device type based on screen width
  static DeviceType getDeviceType(double width) {
    if (width < AppConstants.mobileBreakpoint) {
      return DeviceType.mobile;
    } else if (width < AppConstants.tabletBreakpoint) {
      return DeviceType.tablet;
    } else if (width < AppConstants.desktopBreakpoint) {
      return DeviceType.desktop;
    } else {
      return DeviceType.largeDesktop;
    }
  }

  /// Get responsive padding based on device type
  static EdgeInsets getResponsivePadding(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final deviceType = getDeviceType(width);

    switch (deviceType) {
      case DeviceType.mobile:
        return const EdgeInsets.all(AppConstants.defaultPadding);
      case DeviceType.tablet:
        return const EdgeInsets.all(AppConstants.largePadding);
      case DeviceType.desktop:
        return const EdgeInsets.all(AppConstants.extraLargePadding);
      case DeviceType.largeDesktop:
        return const EdgeInsets.symmetric(
          horizontal: AppConstants.extraLargePadding * 2,
          vertical: AppConstants.extraLargePadding,
        );
    }
  }

  /// Get responsive grid columns
  static int getGridColumns(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final deviceType = getDeviceType(width);

    switch (deviceType) {
      case DeviceType.mobile:
        return AppConstants.mobileColumns;
      case DeviceType.tablet:
        return AppConstants.tabletColumns;
      case DeviceType.desktop:
        return AppConstants.desktopColumns;
      case DeviceType.largeDesktop:
        return AppConstants.largeDesktopColumns;
    }
  }

  /// Get responsive card aspect ratio
  static double getCardAspectRatio(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final deviceType = getDeviceType(width);

    switch (deviceType) {
      case DeviceType.mobile:
        return 3.0;
      case DeviceType.tablet:
        return 2.2;
      case DeviceType.desktop:
        return 2.0;
      case DeviceType.largeDesktop:
        return 1.8;
    }
  }

  /// Get responsive font size
  static double getResponsiveFontSize(BuildContext context, double baseFontSize) {
    final width = MediaQuery.of(context).size.width;
    final deviceType = getDeviceType(width);

    switch (deviceType) {
      case DeviceType.mobile:
        return baseFontSize * 0.9;
      case DeviceType.tablet:
        return baseFontSize;
      case DeviceType.desktop:
        return baseFontSize * 1.1;
      case DeviceType.largeDesktop:
        return baseFontSize * 1.2;
    }
  }

  /// Get responsive spacing
  static double getResponsiveSpacing(BuildContext context, double baseSpacing) {
    final width = MediaQuery.of(context).size.width;
    final deviceType = getDeviceType(width);

    switch (deviceType) {
      case DeviceType.mobile:
        return baseSpacing * 0.8;
      case DeviceType.tablet:
        return baseSpacing;
      case DeviceType.desktop:
        return baseSpacing * 1.2;
      case DeviceType.largeDesktop:
        return baseSpacing * 1.5;
    }
  }

  /// Check if device is mobile
  static bool isMobile(BuildContext context) {
    return getDeviceType(MediaQuery.of(context).size.width) == DeviceType.mobile;
  }

  /// Check if device is tablet
  static bool isTablet(BuildContext context) {
    return getDeviceType(MediaQuery.of(context).size.width) == DeviceType.tablet;
  }

  /// Check if device is desktop
  static bool isDesktop(BuildContext context) {
    final deviceType = getDeviceType(MediaQuery.of(context).size.width);
    return deviceType == DeviceType.desktop || deviceType == DeviceType.largeDesktop;
  }

  /// Get responsive table configuration
  static TableConfig getTableConfig(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final deviceType = getDeviceType(width);

    switch (deviceType) {
      case DeviceType.mobile:
        return TableConfig(
          showAllColumns: false,
          horizontalScrollEnabled: true,
          columnSpacing: 8.0,
          horizontalMargin: 8.0,
        );
      case DeviceType.tablet:
        return TableConfig(
          showAllColumns: true,
          horizontalScrollEnabled: false,
          columnSpacing: 12.0,
          horizontalMargin: 12.0,
        );
      case DeviceType.desktop:
      case DeviceType.largeDesktop:
        return TableConfig(
          showAllColumns: true,
          horizontalScrollEnabled: false,
          columnSpacing: 16.0,
          horizontalMargin: 16.0,
        );
    }
  }

  /// Get responsive dialog width
  static double getDialogWidth(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final deviceType = getDeviceType(width);

    switch (deviceType) {
      case DeviceType.mobile:
        return width * 0.9;
      case DeviceType.tablet:
        return width * 0.7;
      case DeviceType.desktop:
        return width * 0.5;
      case DeviceType.largeDesktop:
        return width * 0.4;
    }
  }

  /// Get responsive sidebar width
  static double getSidebarWidth(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final deviceType = getDeviceType(width);

    switch (deviceType) {
      case DeviceType.mobile:
        return width * 0.8;
      case DeviceType.tablet:
        return 300.0;
      case DeviceType.desktop:
        return 320.0;
      case DeviceType.largeDesktop:
        return 350.0;
    }
  }
}

/// Device type enumeration
enum DeviceType {
  mobile,
  tablet,
  desktop,
  largeDesktop,
}

/// Table configuration class
class TableConfig {
  final bool showAllColumns;
  final bool horizontalScrollEnabled;
  final double columnSpacing;
  final double horizontalMargin;

  const TableConfig({
    required this.showAllColumns,
    required this.horizontalScrollEnabled,
    required this.columnSpacing,
    required this.horizontalMargin,
  });
}

/// Responsive widget builder
class ResponsiveBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, DeviceType deviceType) builder;

  const ResponsiveBuilder({
    super.key,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final deviceType = ResponsiveLayout.getDeviceType(width);
    return builder(context, deviceType);
  }
}

/// Responsive grid widget
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final double? childAspectRatio;
  final double? crossAxisSpacing;
  final double? mainAxisSpacing;
  final EdgeInsetsGeometry? padding;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.childAspectRatio,
    this.crossAxisSpacing,
    this.mainAxisSpacing,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final columns = ResponsiveLayout.getGridColumns(context);
    final aspectRatio = childAspectRatio ?? ResponsiveLayout.getCardAspectRatio(context);
    final crossSpacing = crossAxisSpacing ?? ResponsiveLayout.getResponsiveSpacing(context, 8.0);
    final mainSpacing = mainAxisSpacing ?? ResponsiveLayout.getResponsiveSpacing(context, 8.0);

    return Padding(
      padding: padding ?? ResponsiveLayout.getResponsivePadding(context),
      child: GridView.count(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        crossAxisCount: columns,
        crossAxisSpacing: crossSpacing,
        mainAxisSpacing: mainSpacing,
        childAspectRatio: aspectRatio,
        children: children,
      ),
    );
  }
}
