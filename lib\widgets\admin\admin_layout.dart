import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_constants.dart';
import '../../utils/responsive_layout.dart';
import '../common/modern_drawer.dart';

/// Enhanced admin layout wrapper with responsive design
class AdminLayout extends StatelessWidget {
  final String title;
  final Widget body;
  final List<Widget>? actions;
  final Widget? floatingActionButton;
  final Widget? bottomNavigationBar;
  final bool showDrawer;
  final bool showBackButton;
  final PreferredSizeWidget? bottom;
  final Color? backgroundColor;

  const AdminLayout({
    super.key,
    required this.title,
    required this.body,
    this.actions,
    this.floatingActionButton,
    this.bottomNavigationBar,
    this.showDrawer = true,
    this.showBackButton = false,
    this.bottom,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, deviceType) {
        return Scaffold(
          backgroundColor: backgroundColor ?? AppColors.backgroundGrey,
          appBar: _buildAppBar(context, deviceType),
          drawer: showDrawer ? const ModernDrawer(isAdmin: true) : null,
          body: _buildBody(context, deviceType),
          floatingActionButton: floatingActionButton,
          bottomNavigationBar: bottomNavigationBar,
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context, DeviceType deviceType) {
    return AppBar(
      title: Text(
        title,
        style: TextStyle(
          fontSize: ResponsiveLayout.getResponsiveFontSize(context, 20),
          fontWeight: FontWeight.w600,
          letterSpacing: 0.15,
        ),
      ),
      backgroundColor: AppColors.primaryBlue,
      foregroundColor: AppColors.textWhite,
      elevation: 0,
      scrolledUnderElevation: 1,
      centerTitle: true,
      automaticallyImplyLeading: showBackButton || !showDrawer,
      actions: _buildActions(context, deviceType),
      bottom: bottom,
    );
  }

  List<Widget>? _buildActions(BuildContext context, DeviceType deviceType) {
    if (actions == null) return null;

    // On mobile, show fewer actions and use overflow menu
    if (deviceType == DeviceType.mobile && actions!.length > 2) {
      return [
        ...actions!.take(1),
        PopupMenuButton<int>(
          icon: const Icon(Icons.more_vert),
          itemBuilder: (context) => List.generate(
            actions!.length - 1,
            (index) => PopupMenuItem<int>(
              value: index + 1,
              child: actions![index + 1],
            ),
          ),
          onSelected: (index) {
            // Handle action selection
          },
        ),
      ];
    }

    return actions;
  }

  Widget _buildBody(BuildContext context, DeviceType deviceType) {
    final padding = ResponsiveLayout.getResponsivePadding(context);

    return SafeArea(
      child: Container(
        width: double.infinity,
        height: double.infinity,
        child: body,
      ),
    );
  }
}

/// Enhanced admin card widget with responsive design
class AdminCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? backgroundColor;
  final double? elevation;
  final BorderRadius? borderRadius;
  final Border? border;
  final List<BoxShadow>? boxShadow;
  final VoidCallback? onTap;

  const AdminCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.elevation,
    this.borderRadius,
    this.border,
    this.boxShadow,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, deviceType) {
        final responsivePadding = padding ?? 
            EdgeInsets.all(ResponsiveLayout.getResponsiveSpacing(context, AppConstants.defaultPadding));
        final responsiveMargin = margin ?? 
            EdgeInsets.all(ResponsiveLayout.getResponsiveSpacing(context, AppConstants.smallPadding));

        Widget cardChild = Container(
          padding: responsivePadding,
          margin: responsiveMargin,
          decoration: BoxDecoration(
            color: backgroundColor ?? AppColors.cardBackground,
            borderRadius: borderRadius ?? BorderRadius.circular(AppConstants.borderRadius),
            border: border ?? Border.all(
              color: AppColors.cardBorder.withValues(alpha: 0.2),
              width: 1,
            ),
            boxShadow: boxShadow ?? [
              BoxShadow(
                color: AppColors.cardShadow.withValues(alpha: 0.08),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: child,
        );

        if (onTap != null) {
          cardChild = InkWell(
            onTap: onTap,
            borderRadius: borderRadius ?? BorderRadius.circular(AppConstants.borderRadius),
            child: cardChild,
          );
        }

        return cardChild;
      },
    );
  }
}

/// Enhanced admin section header
class AdminSectionHeader extends StatelessWidget {
  final String title;
  final String? subtitle;
  final Widget? trailing;
  final EdgeInsetsGeometry? padding;
  final bool showDivider;

  const AdminSectionHeader({
    super.key,
    required this.title,
    this.subtitle,
    this.trailing,
    this.padding,
    this.showDivider = true,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, deviceType) {
        final responsivePadding = padding ?? 
            ResponsiveLayout.getResponsivePadding(context);

        return Container(
          padding: responsivePadding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            fontSize: ResponsiveLayout.getResponsiveFontSize(context, 24),
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        if (subtitle != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            subtitle!,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontSize: ResponsiveLayout.getResponsiveFontSize(context, 14),
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  if (trailing != null) trailing!,
                ],
              ),
              if (showDivider) ...[
                SizedBox(height: ResponsiveLayout.getResponsiveSpacing(context, 16)),
                const Divider(color: AppColors.borderLight),
              ],
            ],
          ),
        );
      },
    );
  }
}

/// Enhanced admin stats grid
class AdminStatsGrid extends StatelessWidget {
  final List<AdminStatItem> stats;
  final EdgeInsetsGeometry? padding;

  const AdminStatsGrid({
    super.key,
    required this.stats,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveGrid(
      padding: padding,
      children: stats.map((stat) => AdminStatCard(stat: stat)).toList(),
    );
  }
}

/// Admin stat item data class
class AdminStatItem {
  final String title;
  final String value;
  final IconData icon;
  final Color color;
  final VoidCallback? onTap;
  final String? subtitle;
  final Widget? trailing;

  const AdminStatItem({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    this.onTap,
    this.subtitle,
    this.trailing,
  });
}

/// Enhanced admin stat card
class AdminStatCard extends StatelessWidget {
  final AdminStatItem stat;

  const AdminStatCard({
    super.key,
    required this.stat,
  });

  @override
  Widget build(BuildContext context) {
    return AdminCard(
      onTap: stat.onTap,
      child: ResponsiveBuilder(
        builder: (context, deviceType) {
          return Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: EdgeInsets.all(
                  ResponsiveLayout.getResponsiveSpacing(context, 12),
                ),
                decoration: BoxDecoration(
                  color: stat.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                ),
                child: Icon(
                  stat.icon,
                  color: stat.color,
                  size: ResponsiveLayout.getResponsiveSpacing(context, 24),
                ),
              ),
              SizedBox(height: ResponsiveLayout.getResponsiveSpacing(context, 12)),
              Text(
                stat.value,
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontSize: ResponsiveLayout.getResponsiveFontSize(context, 28),
                  fontWeight: FontWeight.bold,
                  color: stat.color,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: ResponsiveLayout.getResponsiveSpacing(context, 4)),
              Text(
                stat.title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontSize: ResponsiveLayout.getResponsiveFontSize(context, 14),
                  color: AppColors.textSecondary,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              if (stat.subtitle != null) ...[
                SizedBox(height: ResponsiveLayout.getResponsiveSpacing(context, 2)),
                Text(
                  stat.subtitle!,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontSize: ResponsiveLayout.getResponsiveFontSize(context, 12),
                    color: AppColors.textHint,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
              if (stat.trailing != null) ...[
                SizedBox(height: ResponsiveLayout.getResponsiveSpacing(context, 8)),
                stat.trailing!,
              ],
            ],
          );
        },
      ),
    );
  }
}
